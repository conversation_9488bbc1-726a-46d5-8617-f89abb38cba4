// Agent Hu<PERSON>le Pro Analyzer - Background Service Worker
import { API_ENDPOINT } from './config.js';
import { initializeCacheWarming, stopCacheWarming, initializePersistentCacheWarming } from './js/auth/proValidator.js';
import { migrateCacheToPermanent } from './js/auth/permanentProStatus.js';

// Extension installation and startup
chrome.runtime.onInstalled.addListener((details) => {
    console.log('Agent Hustle Pro Analyzer installed:', details.reason);
    
    // Create context menu items
    createContextMenus();
    
    // Initialize smart cache warming for pro validation
    initializeCacheWarming();

    // PHASE 2: Initialize persistent cache warming
    initializePersistentCacheWarming();

    // NEW: Migrate existing Pro users to permanent storage
    try {
        migrateCacheToPermanent().then(result => {
            if (result.migrated) {
                console.log('✅ Successfully migrated existing Pro user to permanent storage');
            }
        }).catch(error => {
            console.warn('⚠️ Migration failed during installation:', error);
        });
    } catch (error) {
        console.warn('⚠️ Migration initialization failed:', error);
    }

    // Show welcome notification on first install
    if (details.reason === 'install') {
        showWelcomeNotification();
    }
});

// Initialize cache warming on startup
chrome.runtime.onStartup.addListener(() => {
    console.log('Extension startup - initializing cache warming');
    initializeCacheWarming();
    initializePersistentCacheWarming();

    // NEW: Migrate existing Pro users to permanent storage on startup
    try {
        migrateCacheToPermanent().then(result => {
            if (result.migrated) {
                console.log('✅ Successfully migrated existing Pro user to permanent storage on startup');
            }
        }).catch(error => {
            console.warn('⚠️ Migration failed during startup:', error);
        });
    } catch (error) {
        console.warn('⚠️ Migration initialization failed on startup:', error);
    }
});

// Clean up cache warming on suspend
chrome.runtime.onSuspend.addListener(() => {
    console.log('Extension suspending - stopping cache warming');
    stopCacheWarming();
});

// PHASE 2: Handle persistent validation alarms
chrome.alarms.onAlarm.addListener(async (alarm) => {
    if (alarm.name === 'proKeyValidation') {
        try {
            console.log('🔥 Alarm-triggered cache refresh');
            const result = await chrome.storage.sync.get(['hustleProKey']);
            const proKey = result.hustleProKey;
            
            if (proKey) {
                // Import validation function and refresh cache
                const { validateProKey } = await import('./js/auth/proValidator.js');
                await validateProKey(proKey, true); // Force validation
                console.log('✅ Background cache refresh completed');
            }
        } catch (error) {
            console.warn('Background cache refresh failed:', error);
        }
    }
});

// Create context menu items
function createContextMenus() {
    console.log('Creating context menus...');
    // Remove existing context menus
    chrome.contextMenus.removeAll(() => {
        if (chrome.runtime.lastError) {
            console.error('Error removing context menus:', chrome.runtime.lastError);
            return;
        }
        console.log('Removed existing context menus');
        
        // Main context menu
        chrome.contextMenus.create({
            id: 'agentHustleAnalyzer',
            title: '🚀 Agent Hustle Pro Analyzer',
            contexts: ['selection', 'page']
        }, () => {
            if (chrome.runtime.lastError) {
                console.error('Error creating main context menu:', chrome.runtime.lastError);
            } else {
                console.log('Main context menu created');
            }
        });

        // Analyze selected text
        chrome.contextMenus.create({
            id: 'analyzeSelection',
            parentId: 'agentHustleAnalyzer',
            title: '📝 Analyze Selected Text',
            contexts: ['selection']
        }, () => {
            if (chrome.runtime.lastError) {
                console.error('Error creating analyzeSelection menu:', chrome.runtime.lastError);
            } else {
                console.log('analyzeSelection menu created');
            }
        });

        // Analyze entire page
        chrome.contextMenus.create({
            id: 'analyzePage',
            parentId: 'agentHustleAnalyzer',
            title: '🌐 Analyze Entire Page',
            contexts: ['page']
        }, () => {
            if (chrome.runtime.lastError) {
                console.error('Error creating analyzePage menu:', chrome.runtime.lastError);
            } else {
                console.log('analyzePage menu created');
            }
        });

        // Separator
        chrome.contextMenus.create({
            id: 'separator1',
            parentId: 'agentHustleAnalyzer',
            type: 'separator',
            contexts: ['selection', 'page']
        }, () => {
            if (chrome.runtime.lastError) {
                console.error('Error creating separator:', chrome.runtime.lastError);
            } else {
                console.log('separator created');
            }
        });

        // Custom Analysis (Pro feature)
        chrome.contextMenus.create({
            id: 'customAnalysis',
            parentId: 'agentHustleAnalyzer',
            title: '🎯 Custom Analysis',
            contexts: ['selection', 'page']
        }, () => {
            if (chrome.runtime.lastError) {
                console.error('Error creating customAnalysis menu:', chrome.runtime.lastError);
            } else {
                console.log('customAnalysis menu created');
            }
        });

        // View History
        chrome.contextMenus.create({
            id: 'viewHistory',
            parentId: 'agentHustleAnalyzer',
            title: '📊 View History',
            contexts: ['selection', 'page']
        }, () => {
            if (chrome.runtime.lastError) {
                console.error('Error creating viewHistory menu:', chrome.runtime.lastError);
            } else {
                console.log('viewHistory menu created');
            }
        });
    });
}

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
    console.log('Context menu clicked:', info.menuItemId);
    
    try {
        // Check if API key is configured
        const result = await chrome.storage.sync.get(['agentHustleApiKey']);
        if (!result.agentHustleApiKey) {
            // Open popup to configure API key
            chrome.action.openPopup();
            return;
        }

        // Handle different menu actions
        switch (info.menuItemId) {
            case 'analyzeSelection':
                await handleAnalyzeSelection(info, tab);
                break;
            case 'analyzePage':
                await handleAnalyzePage(info, tab);
                break;
            case 'customAnalysis':
                await handleCustomAnalysis(info, tab);
                break;
            case 'viewHistory':
                await handleViewHistory(info, tab);
                break;
            default:
                console.log('Unknown context menu item:', info.menuItemId);
                break;
        }
    } catch (error) {
        console.error('Context menu action failed:', error);
        showErrorNotification('Analysis failed. Please try again.');
    }
});

// Context menu handlers
async function handleAnalyzeSelection(info, tab) {
    const selectedText = info.selectionText;
    if (!selectedText) {
        showErrorNotification('No text selected');
        return;
    }

    try {
        // Immediately open the popup
        await chrome.action.openPopup();

        // Give the popup a moment to open and set up listeners
        await new Promise(resolve => setTimeout(resolve, 150)); 
        
        // Send message to popup to trigger analysis
        chrome.runtime.sendMessage({
            action: 'triggerAnalysis',
            type: 'selection',
            data: selectedText
        });
        
    } catch (error) {
        console.error('Could not open popup or send message, running analysis in background:', error);
        
        // Fallback: run analysis in background
        const prompt = `Please analyze the following selected text...`; // Keep fallback for safety
        await performAnalysis(prompt, 'Text Selection Analysis', tab);
    }
}

async function handleAnalyzePage(info, tab) {
    try {
        // Immediately open the popup
        await chrome.action.openPopup();
        
        // Give the popup a moment to open and set up listeners
        await new Promise(resolve => setTimeout(resolve, 150));

        const results = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: () => ({
                title: document.title,
                url: window.location.href,
                content: document.body.innerText.substring(0, 8000)
            })
        });

        const pageData = results[0].result;
        
        // Send message to popup to trigger analysis
        chrome.runtime.sendMessage({
            action: 'triggerAnalysis',
            type: 'page',
            data: pageData
        });
        
    } catch (error) {
        console.error('Could not open popup or send message, running analysis in background:', error);
        
        // Fallback: run analysis in background
        const results = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: () => {
                const title = document.title;
                const url = window.location.href;
                const content = document.body.innerText.substring(0, 8000);
                return { title, url, content };
            }
        });

        const pageData = results[0].result;
        const prompt = `Please analyze the following webpage and provide comprehensive insights:

Page Title: ${pageData.title}
URL: ${pageData.url}

Page Content:
${pageData.content}

Please provide a detailed analysis including:
1. Summary of the page content and purpose
2. Key topics and themes discussed
3. Important information and insights
4. Content quality and credibility assessment
5. Any actionable takeaways or recommendations`;

        await performAnalysis(prompt, 'Full Page Analysis', tab);
    }
}

// New context menu handlers
async function handleCustomAnalysis(info, tab) {
    // Immediately open popup first for consistent UX
    try {
        chrome.action.openPopup();
    } catch (error) {
        console.error('Could not open popup:', error);
        showErrorNotification('Could not open popup. Please click the extension icon.');
        return;
    }

    // Check pro status using storage directly (can't use import in service worker)
    try {
        const result = await chrome.storage.sync.get(['hustleProKey', 'hustleProStatus']);
        const proKey = result.hustleProKey;
        const cachedStatus = result.hustleProStatus;
        
        // Simple pro status check - if no key, definitely not pro
        let isPro = false;
        if (proKey && proKey.trim().length > 0) {
            // Use reduced cache window (30 minutes instead of 1 hour) for consistency
            if (cachedStatus && cachedStatus.isPro && cachedStatus.lastChecked) {
                const lastChecked = new Date(cachedStatus.lastChecked);
                const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000); // Reduced from 1 hour
                if (lastChecked > thirtyMinutesAgo) {
                    isPro = cachedStatus.isPro;
                }
            }
            // If no recent cache, assume pro if key exists (will be validated in popup)
            else if (proKey) {
                isPro = true;
            }
        }
        
        if (!isPro) {
            // Store a flag to show upgrade section when popup opens
            await chrome.storage.local.set({
                showUpgradeSection: true,
                upgradeReason: 'Custom Analysis from context menu'
            });
            return;
        }
        
        // For pro users, store context for custom analysis form
        const selectedText = info.selectionText || '';
        await chrome.storage.local.set({
            showCustomAnalysis: true,
            contextMenuData: {
                selectedText: selectedText,
                hasSelection: !!selectedText,
                tabId: tab.id,
                tabUrl: tab.url
            }
        });
        
    } catch (error) {
        console.error('Error checking pro status:', error);
        // Fallback: show upgrade section
        await chrome.storage.local.set({
            showUpgradeSection: true,
            upgradeReason: 'Custom Analysis from context menu (error checking status)'
        });
    }
}

async function handleViewHistory(info, tab) {
    // Open popup and show history section
    chrome.action.openPopup();
    
    // Store flag to show history section when popup opens
    await chrome.storage.local.set({
        showHistorySection: true
    });
}

// Core shared API call function
async function performAPICall(prompt, analysisType, tab, options = {}) {
    const { isChat = false, sessionId = null } = options;
    
    try {
        console.log(`🔧 Background: performAPICall called with:`, { 
            analysisType, 
            promptLength: prompt.length, 
            isChat,
            sessionId: sessionId || 'none'
        });

        // Get API key
        const result = await chrome.storage.sync.get(['agentHustleApiKey']);
        const apiKey = result.agentHustleApiKey;

        if (!apiKey) {
            console.error(`❌ Background: No API key configured${isChat ? ' for chat' : ''}`);
            throw new Error('API key not configured');
        }

        console.log(`🔑 Background: API key found${isChat ? ' for chat' : ''}, length:`, apiKey.length);

        // Show processing notification
        showProcessingNotification(`${analysisType}${isChat ? ' (Chat)' : ''}`);

        // Make API request with the correct payload structure
        console.log(`📡 Background: Making${isChat ? ' chat' : ''} API request to:`, API_ENDPOINT);
        const response = await fetch(`${API_ENDPOINT}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': apiKey
            },
            body: JSON.stringify({
                messages: [{ role: 'user', content: prompt }],
                vaultId: 'default',
                ...(isChat && { stream: true }) // Enable streaming for chat
            })
        });

        console.log('Response status:', response.status);
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`${isChat ? 'Chat ' : ''}API Error Response:`, errorText);
            throw new Error(`API request failed: ${response.status} - ${errorText}`);
        }

        const responseText = await response.text();
        console.log('Response text length:', responseText.length);
        console.log('Response text preview:', responseText.substring(0, 200));
        
        let result;
        try {
            // The response can be a custom stream format, not a single JSON object.
            // We parse it line by line to extract the content.
            const lines = responseText.split('\n');
            let content = '';
            let hasContentStream = false;

            for (const line of lines) {
                if (line.startsWith('0:')) {
                    hasContentStream = true;
                    try {
                        // Extract the string content, e.g., 0:"Hello "
                        const chunk = JSON.parse(line.substring(2));
                        content += chunk;
                    } catch (e) {
                        console.warn(`Could not parse${isChat ? ' chat' : ''} stream chunk:`, line);
                    }
                }
            }

            if (hasContentStream) {
                result = { content: content };
                console.log(`Parsed${isChat ? ' chat' : ''} streamed content, length:`, content.length);
            } else {
                // Fallback for non-streamed, regular JSON responses
                result = JSON.parse(responseText);
                console.log(`Parsed${isChat ? ' chat' : ''} JSON response`);
            }
        } catch (error) {
            console.error(`Failed to parse${isChat ? ' chat' : ''} response:`, error);
            console.error(`${isChat ? 'Chat r' : 'R'}esponse text:`, responseText);
            throw new Error(`Invalid${isChat ? ' chat' : ''} response from server: ${error.message}`);
        }
        
        // Store result for popup access
        const storageKey = isChat ? 'lastChatMessage' : 'lastAnalysis';
        const storageData = isChat ? {
            sessionId: sessionId,
            type: analysisType,
            result: result,
            timestamp: new Date().toISOString(),
            tabId: tab.id,
            tabUrl: tab.url
        } : {
            type: analysisType,
            result: result,
            timestamp: new Date().toISOString(),
            tabId: tab.id,
            tabUrl: tab.url
        };
        
        await chrome.storage.local.set({
            [storageKey]: storageData
        });

        // Show success notification
        showSuccessNotification(`${analysisType}${isChat ? ' completed' : ''}`);
        
        return result;

    } catch (error) {
        console.error(`${isChat ? 'Chat a' : 'A'}nalysis failed:`, error);
        showErrorNotification(`${analysisType} failed: ${error.message}`);
        throw error; // Re-throw the error to be caught by the caller
    }
}

// Core analysis function - now uses the shared API call function
async function performAnalysis(prompt, analysisType, tab) {
    return performAPICall(prompt, analysisType, tab, { isChat: false });
}

// Perform chat-specific analysis with streaming support - now uses the shared API call function
async function performChatAnalysis(prompt, analysisType, tab, sessionId) {
    return performAPICall(prompt, analysisType, tab, { isChat: true, sessionId });
}

// Build chat prompt from message history
function buildChatPrompt(messages) {
    if (!messages || messages.length === 0) {
        return "You are Agent Hustle, a helpful AI assistant. Please provide helpful and accurate responses.";
    }

    // Build conversation context
    let prompt = "You are Agent Hustle, a helpful AI assistant. Here is the conversation history:\n\n";
    
    // Add previous messages for context (limit to last 10 for performance)
    const recentMessages = messages.slice(-10);
    recentMessages.forEach(msg => {
        const role = msg.role === 'user' ? 'Human' : 'Assistant';
        prompt += `${role}: ${msg.content}\n\n`;
    });

    prompt += "Please continue the conversation as Agent Hustle, providing helpful and accurate responses.";
    return prompt;
}

// Inject analysis panel into the page
async function injectAnalysisPanel(tabId, result, analysisType) {
    try {
        // Inject the analysis panel
        await chrome.scripting.executeScript({
            target: { tabId },
            function: (result, analysisType) => {
                // Remove existing panel if any
                const existingPanel = document.getElementById('agent-hustle-analysis-panel');
                if (existingPanel) {
                    existingPanel.remove();
                }

                // Define helper functions first
                window.formatAnalysisContent = (content) => {
                    if (!content) return '<p>No content available</p>';
                    return content
                        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                        .replace(/\*(.*?)\*/g, '<em>$1</em>')
                        .replace(/`(.*?)`/g, '<code style="background: #f8f9fa; padding: 2px 4px; border-radius: 3px;">$1</code>')
                        .replace(/\n\n/g, '</p><p>')
                        .replace(/\n/g, '<br>')
                        .replace(/^/, '<p>')
                        .replace(/$/, '</p>')
                        .replace(/(\d+\.\s)/g, '<br><strong>$1</strong>');
                };

                window.copyAnalysisToClipboard = () => {
                    const content = result.content || JSON.stringify(result, null, 2);
                    navigator.clipboard.writeText(content).then(() => {
                        // Show copied feedback
                        const copyButton = document.querySelector('#agent-hustle-analysis-panel button[onclick="copyAnalysisToClipboard()"]');
                        if (copyButton) {
                            const originalText = copyButton.textContent;
                            copyButton.textContent = '✅ Copied!';
                            setTimeout(() => {
                                copyButton.textContent = originalText;
                            }, 2000);
                        }
                    }).catch(err => {
                        console.error('Failed to copy to clipboard:', err);
                    });
                };

                // Format the content before using it in HTML
                const formattedContent = window.formatAnalysisContent(result.content || JSON.stringify(result, null, 2));

                // Create analysis panel
                const panel = document.createElement('div');
                panel.id = 'agent-hustle-analysis-panel';
                panel.innerHTML = `
                    <div style="
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        width: 400px;
                        max-height: 600px;
                        background: white;
                        border-radius: 12px;
                        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
                        z-index: 10000;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        border: 1px solid #e1e5e9;
                        overflow: hidden;
                    ">
                        <div style="
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 16px;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        ">
                            <div>
                                <div style="font-weight: 600; font-size: 16px;">🚀 Agent Hustle Pro</div>
                                <div style="font-size: 12px; opacity: 0.9;">${analysisType}</div>
                            </div>
                            <button onclick="this.closest('#agent-hustle-analysis-panel').remove()" style="
                                background: rgba(255,255,255,0.2);
                                border: none;
                                color: white;
                                width: 24px;
                                height: 24px;
                                border-radius: 50%;
                                cursor: pointer;
                                font-size: 14px;
                            ">×</button>
                        </div>
                        <div style="
                            padding: 16px;
                            max-height: 500px;
                            overflow-y: auto;
                            font-size: 14px;
                            line-height: 1.5;
                        ">
                            ${formattedContent}
                        </div>
                        <div style="
                            padding: 12px 16px;
                            border-top: 1px solid #f0f0f0;
                            display: flex;
                            gap: 8px;
                        ">
                            <button onclick="copyAnalysisToClipboard()" style="
                                background: #667eea;
                                color: white;
                                border: none;
                                padding: 8px 12px;
                                border-radius: 6px;
                                font-size: 12px;
                                cursor: pointer;
                                flex: 1;
                            ">📋 Copy</button>
                            <button onclick="this.closest('#agent-hustle-analysis-panel').remove()" style="
                                background: #f8f9fa;
                                color: #6c757d;
                                border: 1px solid #dee2e6;
                                padding: 8px 12px;
                                border-radius: 6px;
                                font-size: 12px;
                                cursor: pointer;
                                flex: 1;
                            ">Close</button>
                        </div>
                    </div>
                `;

                document.body.appendChild(panel);

                // Auto-hide after 30 seconds
                setTimeout(() => {
                    if (document.getElementById('agent-hustle-analysis-panel')) {
                        panel.style.opacity = '0.7';
                    }
                }, 30000);
            },
            args: [result, analysisType]
        });
    } catch (error) {
        console.error('Failed to inject analysis panel:', error);
    }
}

// Notification functions - simplified to avoid permission issues
function createNotification(options, callback) {
    // For now, just use console logging to avoid notification permission issues
    const title = options.title || 'Agent Hustle Pro';
    const message = options.message || 'Notification';
    
    console.log(`🔔 ${title}: ${message}`);
    
    // Try to create notification but don't fail if it doesn't work
    try {
        const notificationOptions = {
            type: 'basic',
            iconUrl: 'icons/icon48.svg',
            title: title,
            message: message
        };
        
        chrome.notifications.create(notificationOptions, (notificationId) => {
            if (chrome.runtime.lastError) {
                console.log('Notification API not available, using console log only');
            }
            if (callback) {
                callback(notificationId);
            }
        });
    } catch (error) {
        console.log('Notifications disabled, using console log only');
        if (callback) {
            callback(null);
        }
    }
}

function showWelcomeNotification() {
    createNotification({
        type: 'basic',
        iconUrl: 'icons/icon48.svg',
        title: '🚀 Agent Hustle Pro Analyzer',
        message: 'Extension installed! Right-click on any text or page to start analyzing with AI.'
    });
}

function showProcessingNotification(analysisType) {
    console.log(`🧠 AI Analysis in Progress: Agent Hustle is performing ${analysisType}...`);
    createNotification({
        type: 'basic',
        iconUrl: 'icons/icon48.svg',
        title: '🧠 AI Analysis in Progress',
        message: `Agent Hustle is performing ${analysisType}...`
    });
}

function showSuccessNotification(analysisType) {
    console.log(`✅ Analysis Complete: ${analysisType} completed successfully!`);
    createNotification({
        type: 'basic',
        iconUrl: 'icons/icon48.svg',
        title: '✅ Analysis Complete',
        message: `${analysisType} completed successfully! Check the results panel.`
    });
}

function showErrorNotification(message) {
    console.error(`❌ Analysis Failed: ${message}`);
    createNotification({
        type: 'basic',
        iconUrl: 'icons/icon48.svg',
        title: '❌ Analysis Failed',
        message: message
    });
}

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
    // This will open the popup automatically due to the manifest configuration
    console.log('Extension icon clicked');
});

// Message handling for communication with popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'getLastAnalysis') {
        chrome.storage.local.get(['lastAnalysis']).then(result => {
            sendResponse(result.lastAnalysis || null); // Ensure null if not found
        });
        return true; // Keep message channel open for async response
    }
    
    if (request.action === 'clearLastAnalysis') {
        chrome.storage.local.remove(['lastAnalysis']).then(() => {
            sendResponse({ success: true });
        });
        return true;
    }

    if (request.action === 'openPopup') {
        // Open the popup
        chrome.action.openPopup().then(() => {
            sendResponse({ success: true });
        }).catch(error => {
            console.error('Failed to open popup:', error);
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'performAnalysis') {
        console.log('🎯 Background: Received performAnalysis request:', request.data);
        const { prompt, analysisType } = request.data;
        // Since we don't have a tab object here, we'll query for the active one.
        chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
            const tab = tabs[0];
            if (tab) {
                try {
                    console.log('🚀 Background: Starting analysis for tab:', tab.id);
                    const result = await performAnalysis(prompt, analysisType, tab);
                    console.log('✅ Background: Analysis completed, sending result');
                    sendResponse({ result });
                } catch (error) {
                    console.error('❌ Background: Analysis failed:', error);
                    sendResponse({ error: error.message });
                }
            } else {
                console.error('❌ Background: No active tab found');
                sendResponse({ error: "No active tab found." });
            }
        });
        return true; // Indicates that the response is sent asynchronously
    }

    if (request.action === 'sendChatMessage') {
        console.log('💬 Background: Received sendChatMessage request:', request.data);
        const { message, sessionId, messages } = request.data;
        
        // Query for the active tab
        chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
            const tab = tabs[0];
            if (tab) {
                try {
                    console.log('🚀 Background: Starting chat message processing for tab:', tab.id);
                    
                    // Build chat context from message history
                    const chatMessages = messages || [];
                    const prompt = buildChatPrompt(chatMessages);
                    
                    const result = await performChatAnalysis(prompt, 'Chat Response', tab, sessionId);
                    console.log('✅ Background: Chat message processed, sending result');
                    sendResponse({ result });
                } catch (error) {
                    console.error('❌ Background: Chat message processing failed:', error);
                    sendResponse({ error: error.message });
                }
            } else {
                console.error('❌ Background: No active tab found for chat');
                sendResponse({ error: "No active tab found." });
            }
        });
        return true; // Indicates that the response is sent asynchronously
    }

    if (request.action === 'analyzeFromContent') {
        // Call the async handler and let it manage sendResponse
        handleAnalyzeFromContent(request, sender.tab, sendResponse)
            .catch(err => { // Catch any unhandled errors from handleAnalyzeFromContent
                console.error("Outer catch for handleAnalyzeFromContent:", err);
                showErrorNotification("An unexpected error occurred during analysis initiation.");
                try {
                    // Check if response can still be sent
                    if (sendResponse) sendResponse({ success: false, error: "Unexpected background error." });
                } catch (e) {
                    console.error("Failed to send error response:", e);
                }
            });
        return true; // Crucial: indicates that sendResponse will be called asynchronously.
    }
});

async function handleAnalyzeFromContent(request, tab, sendResponse) {
    console.log('Message from content script:', request.type, request.data);

    const apiKeyResult = await chrome.storage.sync.get(['agentHustleApiKey']);
    if (!apiKeyResult.agentHustleApiKey) {
        showErrorNotification('API Key not configured. Please set it in the popup.');
        if (sendResponse) sendResponse({ success: false, error: 'API Key not configured' });
        return;
    }

    let prompt = '';
    let analysisType = '';

    try {
        switch (request.type) {
            case 'selection':
                if (!request.data || request.data.trim().length === 0) {
                    showErrorNotification('No text provided for selection analysis.');
                    if (sendResponse) sendResponse({ success: false, error: 'No text provided' });
                    return;
                }
                analysisType = 'Text Selection Analysis';
                prompt = `Please analyze the following selected text and provide insights, key points, and any relevant analysis:\n\nSelected Text:\n${request.data}\n\nPlease provide a comprehensive analysis including:\n1. Summary of the content\n2. Key insights and takeaways\n3. Any important details or patterns\n4. Recommendations or next steps if applicable`;
                break;
            case 'page':
                const pageData = request.data; // { title, url, metaDescription, content, foundKeywords, timestamp }
                analysisType = 'Full Page Analysis';
                prompt = `Please analyze the following webpage and provide comprehensive insights:\n\nPage Title: ${pageData.title}\nURL: ${pageData.url}\n${pageData.metaDescription ? `Meta Description: ${pageData.metaDescription}\n` : ''}Page Content (excerpt):\n${pageData.content}\n\nPlease provide a detailed analysis including:\n1. Summary of the page content and purpose\n2. Key topics and themes discussed\n3. Important information and insights\n4. Content quality and credibility assessment\n5. Any actionable takeaways or recommendations`;
                break;
            case 'crypto':
                const cryptoData = request.data; // { selectedText, pageData }
                const textToAnalyze = cryptoData.selectedText || cryptoData.pageData.content;
                analysisType = 'Crypto & Blockchain Analysis';
                prompt = `Please perform a comprehensive crypto and blockchain analysis:\n\n${cryptoData.selectedText ? `Selected Text:\n${cryptoData.selectedText}` : `Page Content from: ${cryptoData.pageData.title}`}\nURL: ${cryptoData.pageData.url}\n${cryptoData.pageData.foundKeywords && cryptoData.pageData.foundKeywords.length > 0 ? `Detected Crypto Keywords: ${cryptoData.pageData.foundKeywords.join(', ')}\n` : ''}\nContent to analyze:\n${textToAnalyze}\n\nPlease provide a detailed crypto analysis including:\n1. Identification of cryptocurrencies, tokens, or projects mentioned\n2. Security assessment and risk analysis\n3. Market analysis and trends\n4. Investment risks and opportunities\n5. Red flags or warning signs\n6. Recommendations for further research\n\nUse your crypto analysis tools if needed for additional insights.`;
                break;
            default:
                console.error('Unknown analysis type from content script:', request.type);
                showErrorNotification(`Unknown analysis type: ${request.type}`);
                if (sendResponse) sendResponse({ success: false, error: `Unknown analysis type: ${request.type}` });
                return;
        }

        if (prompt && analysisType && tab) {
            await performAnalysis(prompt, analysisType, tab);
            if (sendResponse) sendResponse({ success: true, message: "Analysis initiated by background script." });
        } else {
            console.error('Missing data for analysis from content script:', { prompt, analysisType, tab });
            showErrorNotification('Could not initiate analysis due to missing information.');
            if (sendResponse) sendResponse({ success: false, error: 'Missing data for analysis' });
        }
    } catch (error) {
        console.error('Error in handleAnalyzeFromContent:', error);
        showErrorNotification(`Analysis initiation failed: ${error.message}`);
        if (sendResponse) sendResponse({ success: false, error: `Analysis initiation failed: ${error.message}` });
    }
}

console.log('Agent Hustle Pro Analyzer background script loaded'); 
